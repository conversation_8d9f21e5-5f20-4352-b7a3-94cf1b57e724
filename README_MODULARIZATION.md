# 订单详情页面模块化重构

## 概述
将订单详情页面的服务照片和追加服务功能模块化，提高代码的可维护性和复用性。

## 模块化内容

### 1. 服务照片组件 (service-photos)
**位置**: `components/service-photos/`

**功能**:
- 展示服务前照片
- 展示服务后照片  
- 照片预览功能
- 照片墙入口卡片

**属性**:
- `service-photos`: 服务照片数据对象
- `photo-wall-data`: 照片墙数据对象

**事件**:
- `previewPhoto`: 预览照片事件
- `viewPhotoWall`: 查看照片墙事件

### 2. 追加服务组件 (additional-services)
**位置**: `components/additional-services/`

**功能**:
- 追加服务列表展示
- 服务状态管理
- 操作按钮（查看、支付、删除）

**属性**:
- `additional-services`: 追加服务列表数组

**事件**:
- `viewDetail`: 查看详情事件
- `payService`: 支付服务事件
- `deleteService`: 删除服务事件

## 使用方式

### 在页面中引用组件

1. 在页面的 `index.json` 中注册组件:
```json
{
  "usingComponents": {
    "service-photos": "/components/service-photos/service-photos",
    "additional-services": "/components/additional-services/additional-services"
  }
}
```

2. 在页面的 `index.wxml` 中使用组件:
```xml
<!-- 服务照片组件 -->
<service-photos
  service-photos="{{servicePhotos}}"
  photo-wall-data="{{photoWallData}}"
  bind:previewPhoto="previewServicePhoto"
  bind:viewPhotoWall="viewPhotoWallDetail"
></service-photos>

<!-- 追加服务组件 -->
<additional-services
  wx:if="{{orderDetail.status === '服务中'}}"
  additional-services="{{additionalServices}}"
  bind:viewDetail="viewAdditionalServiceDetail"
  bind:payService="payAdditionalService"
  bind:deleteService="deleteAdditionalService"
></additional-services>
```

3. 在页面的 `index.js` 中处理组件事件:
```javascript
// 预览服务照片 - 组件事件处理
previewServicePhoto(e) {
  const { url, type, photos } = e.detail;
  // 处理逻辑...
},

// 查看照片墙详情 - 组件事件处理  
viewPhotoWallDetail(e) {
  const { photoWallData } = e.detail;
  // 处理逻辑...
},

// 追加服务相关事件处理
viewAdditionalServiceDetail(e) {
  const { id } = e.detail;
  // 处理逻辑...
},

payAdditionalService(e) {
  const { id } = e.detail;
  // 处理逻辑...
},

deleteAdditionalService(e) {
  const { id, name } = e.detail;
  // 处理逻辑...
}
```

## 优势

1. **代码分离**: 将复杂的订单详情页面拆分为独立的功能模块
2. **可复用性**: 组件可以在其他页面中复用
3. **可维护性**: 每个组件职责单一，便于维护和调试
4. **可扩展性**: 后续可以轻松添加新的功能模块
5. **样式隔离**: 每个组件有独立的样式文件，避免样式冲突

## 文件结构

```
components/
├── service-photos/
│   ├── service-photos.wxml
│   ├── service-photos.js
│   ├── service-photos.json
│   └── service-photos.wxss
└── additional-services/
    ├── additional-services.wxml
    ├── additional-services.js
    ├── additional-services.json
    └── additional-services.wxss

pages/serviceOrder/orderDetail/
├── index.wxml (已简化)
├── index.js (事件处理方法已更新)
├── index.json (已注册组件)
└── index.wxss (已移除组件相关样式)
```

## 注意事项

1. 组件事件处理方法需要从 `e.detail` 中获取数据，而不是 `e.currentTarget.dataset`
2. 组件内部使用 `this.properties` 获取传入的属性数据
3. 组件样式已从主页面样式文件中移除，避免重复定义
4. 保持组件的独立性，避免直接依赖父页面的数据结构
