<!-- 追加服务卡片 -->
<view wx:if="{{additionalServices.length > 0}}" class="additional-services-section">
  <view class="section-title">追加服务</view>
  <view class="additional-services-list">
    <view wx:for="{{additionalServices}}" wx:key="id" class="additional-service-card">
      <!-- 服务头部信息 -->
      <view class="service-header">
        <view class="service-info">
          <text class="service-name">{{item.details[0].serviceName}}</text>
          <text class="service-time">{{item.createdAt}}</text>
        </view>
        <view class="service-status" style="color: {{item.statusInfo.color}}">
          {{item.statusInfo.text}}
        </view>
      </view>

      <!-- 服务价格信息 -->
      <view class="service-price">
        <view class="price-item">
          <text class="price-label">原价：</text>
          <text class="price-value">¥{{item.originalPrice}}</text>
        </view>
        <view class="price-item">
          <text class="price-label">实付：</text>
          <text class="price-value highlight">¥{{item.totalFee}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="service-actions">
        <view wx:for="{{item.statusInfo.actions}}" wx:for-item="action" wx:key="*this" class="action-btn-small">
          <view wx:if="{{action === 'view'}}"
                class="btn-outline"
                bindtap="onViewDetail"
                data-id="{{item.id}}">
            查看
          </view>
          <view wx:elif="{{action === 'pay'}}"
                class="btn-primary"
                bindtap="onPayService"
                data-id="{{item.id}}">
            付款
          </view>
          <view wx:elif="{{action === 'delete'}}"
                class="btn-danger"
                bindtap="onDeleteService"
                data-id="{{item.id}}"
                data-name="{{item.details[0].serviceName}}">
            删除
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
