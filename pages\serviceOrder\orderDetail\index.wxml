<view class="container">
  <view class="order-info">
    <view class="order-content" wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}">
      <image class="product-image" src="{{orderDetail.orderDetails[0].service.logo}}"></image>
      <view class="product-info">
        <view class="flex align-center justify-between">
          <text class="product-name">{{orderDetail.orderDetails[0].service.serviceName}}</text>
          <text class="product-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
        </view>
        <view class="flex align-center justify-between">
          <text wx:if="{{!!orderDetail.orderDetails[0].additionalServices.length}}" class="product-service">增项服务：<text wx:for="{{orderDetail.orderDetails[0].additionalServices}}" wx:for-item="val" wx:key="val.id">{{val.name}};</text></text>
          <text wx:else class="product-service">增项服务：无</text>
          <!-- <text class="product-quantity">x 1</text> -->
        </view>
      </view>
    </view>

    <view class="order-details">
      <view class="detail-item" wx:if="{{orderDetail.originalPrice}}">
        <text class="label">原价</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.originalPrice}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">服务总价</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">实付款</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">订单编号</text>
        <text class="content">{{orderDetail.sn}}</text>
      </view>
      <view wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}" class="detail-item">
        <text class="label">服务宠物</text>
        <text class="content">{{orderDetail.orderDetails[0].petName}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务人员</text>
        <text class="content">{{orderDetail.employee.name}}</text>
      </view>
      <view class="detail-item">
        <text class="label">期望上门时间</text>
        <view class="time-content">
          <view wx:if="{{canModifyTime}}" class="edit-btn" bindtap="modifyServiceTime">
            <text class="edit-text">修改</text>
          </view>
          <text class="content {{canModifyTime ? '' : 'full'}}">{{orderDetail.serviceTime || '待预约'}}</text>
        </view>
      </view>
      <view class="detail-item">
        <text class="label">服务地址</text>
        <view class="address-content">
          <view wx:if="{{canModifyAddress}}" class="edit-btn" bindtap="modifyServiceAddress">
            <text class="edit-text">修改</text>
          </view>
          <text class="content {{canModifyAddress ? '' : 'full'}}">{{orderDetail.address}}</text>
        </view>
      </view>
      <view class="detail-item">
        <text class="label">下单时间</text>
        <text class="content">{{orderDetail.orderTime}}</text>
      </view>
      <view wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0 && orderDetail.orderDetails[0].userRemark}}" class="detail-item">
        <text class="label">用户备注</text>
        <text class="content">{{orderDetail.orderDetails[0].userRemark}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">付款时间</text>
        <text class="content">{{orderDetail.payAt}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">服务时间</text>
        <text class="content">{{orderDetail.serviceAt}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">完成时间</text>
        <text class="content">{{orderDetail.dealAt}}</text>
      </view>
    </view>
  </view>

  <!-- 服务照片组件 -->
  <service-photos
    service-photos="{{servicePhotos}}"
    photo-wall-data="{{photoWallData}}"
    bind:previewPhoto="previewServicePhoto"
    bind:viewPhotoWall="viewPhotoWallDetail"
  ></service-photos>

  <!-- 追加服务组件 -->
  <additional-services
    additional-services="{{additionalServices}}"
    bind:viewDetail="viewAdditionalServiceDetail"
    bind:payService="payAdditionalService"
    bind:deleteService="deleteAdditionalService"
  ></additional-services>

  <!-- 更多操作弹窗 -->
  <view wx:if="{{showMoreActions}}" class="more-actions-dropdown">
    <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.orderId}}">
      更改服务地址
    </view>
    <view class="dropdown-item" bindtap="deleteOrder" data-order-id="{{item.orderId}}">
      更换服务人员
    </view>
    <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.orderId}}">
      取消订单
    </view>
  </view>
  <view class="diygw-col-24 diygw-bottom flex1-clz">
    <!-- 双按钮布局 -->
    <view class="dual-button-layout">
      <!-- 左侧联系按钮 -->
      <view wx:if="{{canCallEmployee}}" class="modern-action-btn contact-btn" bindtap="callEmployee">
        <image src="//xian7.zos.ctyun.cn/pet/static/dianhua.png" class="btn-icon"></image>
        <text class="btn-text">联系服务人员</text>
      </view>

      <!-- 右侧主要操作按钮 -->
      <block wx:if="{{orderDetail.status === '待付款'}}">
        <view class="modern-action-btn pay-btn" bindtap="payOrder" data-sn="{{orderDetail.sn}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/money.png" class="btn-icon"></image>
          <text class="btn-text">去付款</text>
        </view>
      </block>

      <block wx:elif="{{orderDetail.status === 'paid'}}">
        <view class="modern-action-btn urge-btn" bindtap="confirmReceipt" data-order-id="{{orderDetail.orderId}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/shijian1.png" class="btn-icon"></image>
          <text class="btn-text">催接单</text>
        </view>
      </block>

      <block wx:elif="{{orderDetail.status === '服务中' && orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}">
        <view class="modern-action-btn additional-service-btn" bindtap="applyAdditionalService" data-order-detail-id="{{orderDetail.orderDetails[0].id}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/roundAdd.png" class="btn-icon"></image>
          <text class="btn-text">申请追加服务</text>
        </view>
      </block>

      <block wx:elif="{{orderDetail.status === 'completed'}}">
        <view class="modern-action-btn review-btn" bindtap="reviewOrder" data-order-id="{{orderDetail.orderId}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/qt.png" class="btn-icon"></image>
          <text class="btn-text">去评价</text>
        </view>
      </block>
    </view>
  </view>
  <!-- 地址修改弹窗组件 -->
  <address-modify-modal
    show="{{showAddressModal}}"
    title="修改服务地址"
    initial-address="{{addressModalData}}"
    user-info="{{userInfo}}"
    show-coordinates="{{true}}"
    address-placeholder="陕西省西安市雁塔区潘家庄街道大寨路39号"
    detail-placeholder="陕西省西安市雁塔区潘家庄街道大寨路39号"
    remark-placeholder="小区东门"
    loading-text="修改中..."
    bind:close="closeAddressModal"
    bind:confirm="handleAddressModifyConfirm"
  ></address-modify-modal>

  <!-- 时间修改弹窗组件 -->
  <time-modify-modal
    show="{{showTimeModal}}"
    title="修改服务时间"
    initial-time="{{timeModalData}}"
    loading-text="修改中..."
    bind:close="closeTimeModal"
    bind:confirm="handleTimeModifyConfirm"
  ></time-modify-modal>

  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
    bind:handlePayConfirm="handlePayConfirm"
    bind:handlePayModalCancel="handlePayModalCancel"
    bind:handleAdditionalServicePaymentSuccess="handleAdditionalServicePaymentSuccess"
  ></custom-modal>
</view>