<!-- 服务照片区域 -->
<view wx:if="{{servicePhotos && ((servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0) || (servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0))}}" class="service-photos">
  <!-- 服务前照片 -->
  <view wx:if="{{servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0}}" class="photo-section">
    <view class="section-title">
      <text class="title-text">服务前照片</text>
      <text class="photo-time" wx:if="{{servicePhotos.beforePhotoTime}}">{{servicePhotos.beforePhotoTime}}</text>
    </view>
    <view class="photo-grid">
      <view wx:for="{{servicePhotos.beforePhotos}}" wx:key="index" class="photo-item">
        <image
          src="{{item}}"
          class="photo-preview"
          mode="aspectFill"
          bindtap="onPreviewPhoto"
          data-url="{{item}}"
          data-type="before"
        ></image>
      </view>
    </view>
  </view>

  <!-- 服务后照片 -->
  <view wx:if="{{servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0}}" class="photo-section">
    <view class="section-title">
      <text class="title-text">服务后照片</text>
      <text class="photo-time" wx:if="{{servicePhotos.afterPhotoTime}}">{{servicePhotos.afterPhotoTime}}</text>
    </view>
    <view class="photo-grid">
      <view wx:for="{{servicePhotos.afterPhotos}}" wx:key="index" class="photo-item">
        <image
          src="{{item}}"
          class="photo-preview"
          mode="aspectFill"
          bindtap="onPreviewPhoto"
          data-url="{{item}}"
          data-type="after"
        ></image>
      </view>
    </view>
  </view>


</view>
