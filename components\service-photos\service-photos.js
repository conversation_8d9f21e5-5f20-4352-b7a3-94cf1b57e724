Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 服务照片数据
    servicePhotos: {
      type: Object,
      value: null
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 预览服务照片
     */
    onPreviewPhoto(e) {
      const { url, type } = e.currentTarget.dataset;
      const { servicePhotos } = this.properties;

      if (!servicePhotos || !url || !type) return;

      // 根据类型获取对应的照片数组
      const photos = type === 'before' ? servicePhotos.beforePhotos : servicePhotos.afterPhotos;

      if (!photos || photos.length === 0) return;

      // 触发父组件事件
      this.triggerEvent('previewPhoto', {
        url,
        type,
        photos
      });
    },


  }
});
